<template>
  <div class="h-full flex flex-col">
    <div class="mt4 h-0 flex flex-1 flex-col bg-bg-container p4">
      <c-pro-table
        ref="tableRef"
        :api="getChartList"
        :columns="columns"
        :serial-number="true"
        row-key="id"
        immediate
        bordered
      >
        <template #header>
          <a-button type="primary" @click="openEditModal()">新增图表</a-button>
        </template>
        <template #bodyCell="{ record, column }">
          <template v-if="column.key === 'chartType'">
            {{ record.chartType?.name || '未设置' }}
          </template>
          <template v-else-if="column.key === 'dataset'">
            {{ record.dataset?.name || '未设置' }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a @click="openEditModal(record)">编辑</a>
            <a-divider type="vertical" />
            <a @click="previewChart(record)">预览</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除该图表？" @confirm="deleteChart(record)">
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </c-pro-table>
    </div>

    <!-- 新增/编辑弹窗 -->
    <c-modal
      v-model:open="editVisible"
      title="图表编辑"
      width="1200px"
      @ok="handleEditOk"
      @cancel="editVisible = false"
    >
      <a-form :model="editForm" layout="vertical" class="w-full">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="图表标题" required>
              <a-input v-model:value="editForm.title" placeholder="请输入图表标题" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="图表模板" required>
              <a-select
                v-model:value="editForm.chartTypeId"
                placeholder="请选择图表模板"
                @change="onChartTypeChange"
              >
                <a-select-option
                  v-for="template in chartTemplates"
                  :key="template.id"
                  :value="template.id"
                >
                  {{ template.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="数据集" required>
              <a-select
                v-model:value="editForm.chartDatasetId"
                placeholder="请选择数据集"
                @change="onDatasetChange"
              >
                <a-select-option
                  v-for="dataset in datasets"
                  :key="dataset.id"
                  :value="dataset.id"
                >
                  {{ dataset.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <a-tabs v-model:active-key="activeTab" class="mt-4">
        <a-tab-pane key="fieldMapping" tab="字段映射">
          <div v-if="selectedTemplate && selectedDataset">
            <div class="mb-4 flex items-center justify-between">
              <div class="text-sm text-gray-600">
                根据图表模板配置字段映射关系，将数据集字段映射到图表所需的配置字段
              </div>
              <a-button size="small" @click="suggestFieldMapping">
                <template #icon>
                  <i class="i-ant-design:bulb-outlined" />
                </template>
                智能匹配
              </a-button>
            </div>
            <div v-for="(config, index) in fieldConfigs" :key="index" class="mb-4 border border-gray-200 rounded p-4">
              <div class="mb-2 flex items-center">
                <span class="mr-2 text-gray-700 font-medium">{{ getFieldMappingDisplayName(config.fieldMapName) }}</span>
                <a-tag :color="getFieldMappingDataType(config.fieldMapName) === 5 ? 'blue' : 'green'">
                  {{ getFieldMappingDataTypeText(config.fieldMapName) }}
                </a-tag>
              </div>

              <!-- 简单字段映射 -->
              <template v-if="!isComplexField(config.fieldMapName)">
                <a-select
                  v-model:value="config.dataFieldName"
                  style="width: 100%"
                  placeholder="请选择字段"
                  allow-clear
                >
                  <a-select-option
                    v-for="field in getAvailableFields(config.fieldMapName)"
                    :key="field.name"
                    :value="field.name"
                  >
                    {{ field.name }} ({{ field.description || '无描述' }})
                  </a-select-option>
                </a-select>
              </template>

              <!-- 复杂字段映射（对象数组） -->
              <template v-else>
                <div class="rounded bg-gray-50 p-3">
                  <div class="mb-2 text-sm text-gray-600">
                    该字段需要配置多个子字段，请为每个子字段选择对应的数据集字段：
                  </div>
                  <div v-for="(childMapping, childIndex) in getChildMappings(config.fieldMapName)" :key="childIndex" class="mb-3">
                    <div class="mb-1 flex items-center">
                      <span class="mr-2 w-20 text-sm text-gray-600 font-medium">
                        {{ childMapping.displayName }}:
                      </span>
                      <a-select
                        :value="getChildFieldValue(config, childMapping.name)"
                        style="flex: 1"
                        placeholder="请选择字段"
                        allow-clear
                        @change="(value) => setChildFieldValue(config, childMapping.name, value)"
                      >
                        <a-select-option
                          v-for="field in getAvailableFields(childMapping.name)"
                          :key="field.name"
                          :value="field.name"
                        >
                          {{ field.name }} ({{ field.description || '无描述' }})
                        </a-select-option>
                      </a-select>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div v-else class="py-8 text-center text-gray-500">
            请先选择图表模板和数据集
          </div>
        </a-tab-pane>

        <a-tab-pane key="styleConfig" tab="样式配置">
          <div v-if="selectedTemplate">
            <a-table
              :data-source="styleConfigs"
              :columns="styleConfigColumns"
              :pagination="false"
              bordered
              size="small"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'dataFieldName'">
                  <a-input v-model:value="record.dataFieldName" placeholder="请输入配置值" />
                </template>
              </template>
            </a-table>
          </div>
          <div v-else class="py-8 text-center text-gray-500">
            请先选择图表模板
          </div>
        </a-tab-pane>

        <a-tab-pane key="pivotConfig" tab="数据透视">
          <div v-if="selectedDataset">
            <div class="mb-4 flex items-center justify-between">
              <div class="text-sm text-gray-600">
                配置数据透视表，支持多维度分析和数据聚合
              </div>
              <div class="flex gap-2">
                <a-button size="small" @click="clearPivotConfig">
                  <template #icon>
                    <i class="i-ant-design:clear-outlined" />
                  </template>
                  清空配置
                </a-button>
                <a-button size="small" @click="suggestPivotConfig">
                  <template #icon>
                    <i class="i-ant-design:bulb-outlined" />
                  </template>
                  智能推荐
                </a-button>
              </div>
            </div>

            <!-- 行维度配置 -->
            <div class="mb-6">
              <div class="mb-2 flex items-center">
                <span class="mr-2 text-gray-700 font-medium">行维度</span>
                <a-tooltip title="选择作为行的字段，支持多选">
                  <i class="i-ant-design:question-circle-outlined text-gray-400" />
                </a-tooltip>
              </div>
              <a-select
                v-model:value="pivotConfig.rowDimensions"
                mode="multiple"
                style="width: 100%"
                placeholder="请选择行维度字段"
                allow-clear
              >
                <a-select-option
                  v-for="field in availableFields"
                  :key="field.name"
                  :value="field.name"
                >
                  {{ field.name }} ({{ field.description || '无描述' }})
                </a-select-option>
              </a-select>
            </div>

            <!-- 列维度配置 -->
            <div class="mb-6">
              <div class="mb-2 flex items-center">
                <span class="mr-2 text-gray-700 font-medium">列维度</span>
                <a-tooltip title="选择作为列的字段，支持多选">
                  <i class="i-ant-design:question-circle-outlined text-gray-400" />
                </a-tooltip>
              </div>
              <a-select
                v-model:value="pivotConfig.columnDimensions"
                mode="multiple"
                style="width: 100%"
                placeholder="请选择列维度字段"
                allow-clear
              >
                <a-select-option
                  v-for="field in availableFields"
                  :key="field.name"
                  :value="field.name"
                >
                  {{ field.name }} ({{ field.description || '无描述' }})
                </a-select-option>
              </a-select>
            </div>

            <!-- 指标配置 -->
            <div class="mb-6">
              <div class="mb-2 flex items-center justify-between">
                <div class="flex items-center">
                  <span class="mr-2 text-gray-700 font-medium">指标配置</span>
                  <a-tooltip title="配置需要聚合计算的指标字段">
                    <i class="i-ant-design:question-circle-outlined text-gray-400" />
                  </a-tooltip>
                </div>
                <a-button size="small" @click="addMetric">
                  <template #icon>
                    <i class="i-ant-design:plus-outlined" />
                  </template>
                  添加指标
                </a-button>
              </div>
              <div v-if="pivotConfig.metrics && pivotConfig.metrics.length > 0" class="space-y-3">
                <div
                  v-for="(metric, index) in pivotConfig.metrics"
                  :key="index"
                  class="flex items-center gap-3 border border-gray-200 rounded p-3"
                >
                  <div class="flex-1">
                    <a-select
                      v-model:value="metric.valueField"
                      style="width: 100%"
                      placeholder="选择指标字段"
                    >
                      <a-select-option
                        v-for="field in numericFields"
                        :key="field.name"
                        :value="field.name"
                      >
                        {{ field.name }} ({{ field.description || '无描述' }})
                      </a-select-option>
                    </a-select>
                  </div>
                  <div class="w-32">
                    <a-select
                      v-model:value="metric.aggregation"
                      style="width: 100%"
                      placeholder="聚合方式"
                    >
                      <a-select-option :value="0">无</a-select-option>
                      <a-select-option :value="1">求和</a-select-option>
                      <a-select-option :value="2">计数</a-select-option>
                      <a-select-option :value="3">平均值</a-select-option>
                      <a-select-option :value="4">最大值</a-select-option>
                      <a-select-option :value="5">最小值</a-select-option>
                    </a-select>
                  </div>
                  <a-button size="small" danger @click="removeMetric(index)">
                    <template #icon>
                      <i class="i-ant-design:delete-outlined" />
                    </template>
                  </a-button>
                </div>
              </div>
              <div v-else class="border border-gray-300 rounded border-dashed py-8 text-center text-gray-500">
                暂无指标配置，点击"添加指标"按钮开始配置
              </div>
            </div>

            <!-- 筛选条件配置 -->
            <div class="mb-6">
              <div class="mb-2 flex items-center justify-between">
                <div class="flex items-center">
                  <span class="mr-2 text-gray-700 font-medium">筛选条件</span>
                  <a-tooltip title="配置数据过滤条件">
                    <i class="i-ant-design:question-circle-outlined text-gray-400" />
                  </a-tooltip>
                </div>
                <a-button size="small" @click="addFilter">
                  <template #icon>
                    <i class="i-ant-design:plus-outlined" />
                  </template>
                  添加筛选
                </a-button>
              </div>
              <div v-if="pivotConfig.filters && pivotConfig.filters.length > 0" class="space-y-3">
                <div
                  v-for="(filter, index) in pivotConfig.filters"
                  :key="index"
                  class="flex items-center gap-3 border border-gray-200 rounded p-3"
                >
                  <div class="flex-1">
                    <a-select
                      v-model:value="filter.field"
                      style="width: 100%"
                      placeholder="选择字段"
                    >
                      <a-select-option
                        v-for="field in availableFields"
                        :key="field.name"
                        :value="field.name"
                      >
                        {{ field.name }} ({{ field.description || '无描述' }})
                      </a-select-option>
                    </a-select>
                  </div>
                  <div class="w-32">
                    <a-select
                      v-model:value="filter.operator"
                      style="width: 100%"
                      placeholder="操作符"
                    >
                      <a-select-option :value="FilterOperator.Equal">等于</a-select-option>
                      <a-select-option :value="FilterOperator.NotEqual">不等于</a-select-option>
                      <a-select-option :value="FilterOperator.GreaterThan">大于</a-select-option>
                      <a-select-option :value="FilterOperator.GreaterThanOrEqual">大于等于</a-select-option>
                      <a-select-option :value="FilterOperator.LessThan">小于</a-select-option>
                      <a-select-option :value="FilterOperator.LessThanOrEqual">小于等于</a-select-option>
                      <a-select-option :value="FilterOperator.Contains">包含</a-select-option>
                      <a-select-option :value="FilterOperator.NotContains">不包含</a-select-option>
                      <a-select-option :value="FilterOperator.Like">模糊匹配</a-select-option>
                      <a-select-option :value="FilterOperator.NotLike">不匹配</a-select-option>
                    </a-select>
                  </div>
                  <div class="flex-1">
                    <a-input
                      v-model:value="filter.value"
                      placeholder="筛选值"
                    />
                  </div>
                  <a-button size="small" danger @click="removeFilter(index)">
                    <template #icon>
                      <i class="i-ant-design:delete-outlined" />
                    </template>
                  </a-button>
                </div>
              </div>
              <div v-else class="border border-gray-300 rounded border-dashed py-8 text-center text-gray-500">
                暂无筛选条件，点击"添加筛选"按钮开始配置
              </div>
            </div>
          </div>
          <div v-else class="py-8 text-center text-gray-500">
            请先选择数据集
          </div>
        </a-tab-pane>

        <a-tab-pane key="preview" tab="预览效果">
          <div class="h-96 border border-gray-200 rounded">
            <div ref="chartPreviewRef" class="h-full w-full" />
          </div>
          <div class="mt-4 text-center">
            <a-button type="primary" :loading="previewLoading" @click="refreshPreview">
              <template #icon>
                <i class="i-ant-design:reload-outlined" />
              </template>
              实时预览
            </a-button>
            <div class="mt-2 text-xs text-gray-500">
              基于当前配置生成预览效果
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </c-modal>
  </div>
</template>

<script lang="ts" setup>
import type { ChartDataset } from '@/.generated/models/ChartDataset'
import type { ChartTemplate } from '@/.generated/models/ChartTemplate'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import { ChartStatistices } from '@/.generated/apis'
import { AggregationType } from '@/.generated/models/AggregationType'
import { ChartChartManagement } from '@/.generated/models/ChartChartManagement'
import { ChartConfig } from '@/.generated/models/ChartConfig'

import { ChartFieldType } from '@/.generated/models/ChartFieldType'
import { ConfigType } from '@/.generated/models/ConfigType'
import { FilterCondition } from '@/.generated/models/FilterCondition'
import { FilterOperator } from '@/.generated/models/FilterOperator'
import { Metric } from '@/.generated/models/Metric'
import { PivotConfig } from '@/.generated/models/PivotConfig'
import { Guid } from '@/utils/GUID'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import { computed, h, nextTick, onMounted, ref } from 'vue'

definePage({
  meta: {
    layout: 'admin',
    title: '图表实例',
    local: true,
    icon: 'BarChartOutlined',
    order: 2,
  },
})

const tableRef = useTemplateRef('tableRef')
const chartPreviewRef = useTemplateRef('chartPreviewRef')

const columns = ref<ColumnProps[]>([
  {
    title: '图表标题',
    dataIndex: 'title',
    key: 'title',
    search: {
      el: 'input',
      method: 'GET',
      attrs: { placeholder: '请输入图表标题' },
    },
  },
  { title: '图表模板', key: 'chartType', width: 120 },
  { title: '数据集', key: 'dataset', width: 120 },
  { title: '更新时间', dataIndex: 'updatedAt', key: 'updatedAt', width: 180 },
  { title: '操作', key: 'action', width: 150 },
])

// 获取图表列表
async function getChartList(params: any) {
  const res = await ChartStatistices.GetChartsAsync(params)
  return {
    items: res.items,
    totals: res.totals,
    offset: params.offset,
    limit: params.limit,
  }
}

const editVisible = ref(false)
const activeTab = ref('fieldMapping')
const editForm = ref<Partial<ChartChartManagement>>({})
const previewLoading = ref(false)
let chartInstance: any = null

// 图表模板和数据集选项
const chartTemplates = ref<ChartTemplate[]>([])
const datasets = ref<ChartDataset[]>([])

// 当前选中的模板和数据集
const selectedTemplate = computed(() =>
  chartTemplates.value.find(t => t.id === editForm.value.chartTypeId),
)
const selectedDataset = computed(() =>
  datasets.value.find(d => d.id === editForm.value.chartDatasetId),
)

// 字段映射配置
const fieldConfigs = ref<ChartConfig[]>([])

// 获取字段映射的显示名称
function getFieldMappingDisplayName(fieldName: string | null | undefined): string {
  if (!selectedTemplate.value?.fieldMappings || !fieldName)
    return fieldName || ''

  const mapping = selectedTemplate.value.fieldMappings.find(fm => fm.name === fieldName)
  return mapping?.displayName || fieldName
}

// 获取字段映射的数据类型
function getFieldMappingDataType(fieldName: string | null | undefined): number {
  if (!selectedTemplate.value?.fieldMappings || !fieldName)
    return 0

  const mapping = selectedTemplate.value.fieldMappings.find(fm => fm.name === fieldName)
  return mapping?.dataType || 0
}

// 获取字段映射数据类型的文本描述
function getFieldMappingDataTypeText(fieldName: string | null | undefined): string {
  const dataType = getFieldMappingDataType(fieldName)
  const typeMap: Record<number, string> = {
    0: '字符串',
    1: '数字',
    2: '布尔值',
    3: '字符串数组',
    4: '数字数组',
    5: '对象数组',
  }
  return typeMap[dataType] || '未知'
}

// 判断是否为复杂字段（对象数组）
function isComplexField(fieldName: string | null | undefined): boolean {
  return getFieldMappingDataType(fieldName) === 5 // ObjectArray
}

// 获取子字段映射配置
function getChildMappings(fieldName: string | null | undefined) {
  if (!selectedTemplate.value?.fieldMappings || !fieldName)
    return []

  const mapping = selectedTemplate.value.fieldMappings.find(fm => fm.name === fieldName)
  return mapping?.child || []
}

// 获取可用的数据集字段（根据字段类型过滤）
function getAvailableFields(_fieldName: string | null | undefined) {
  if (!selectedDataset.value?.fieldsJson)
    return []

  // 这里可以根据字段类型进行过滤，暂时返回所有字段
  return selectedDataset.value.fieldsJson
}

// 获取子字段的值
function getChildFieldValue(config: ChartConfig, childFieldName: string | null | undefined): string | null {
  if (!config.children || !Array.isArray(config.children) || !childFieldName)
    return null

  const childConfig = config.children.find((item: ChartConfig) => item.fieldMapName === childFieldName)
  return childConfig?.dataFieldName || null
}

// 设置子字段的值
function setChildFieldValue(config: ChartConfig, childFieldName: string | null | undefined, value: string | null) {
  if (!childFieldName)
    return

  // 确保 config.children 是数组
  if (!Array.isArray(config.children)) {
    config.children = []
  }

  // 查找现有的子字段配置
  const existingIndex = config.children.findIndex((item: ChartConfig) => item.fieldMapName === childFieldName)

  if (existingIndex >= 0) {
    // 更新现有配置
    config.children[existingIndex].dataFieldName = value
  }
  else {
    // 添加新的子字段配置
    const childConfig = new ChartConfig()
    childConfig.fieldMapName = childFieldName
    childConfig.dataFieldName = value
    childConfig.defaultValue = null
    config.children.push(childConfig)
  }
}

// 样式配置
const styleConfigs = ref<ChartConfig[]>([])
const styleConfigColumns = ref([
  { title: '样式属性', dataIndex: 'fieldMapName', key: 'fieldMapName' },
  { title: '配置值', dataIndex: 'dataFieldName', key: 'dataFieldName' },
])

// 数据透视配置
const pivotConfig = ref<PivotConfig>(new PivotConfig())

// 计算属性：可用字段

const availableFields = computed(() => {
  return selectedDataset.value?.fieldsJson || []
})

// 计算属性：数值类型字段（用于指标配置）
const numericFields = computed(() => {
  return availableFields.value.filter(field => field.type === ChartFieldType.Number)
})

// 加载图表模板和数据集选项
async function loadOptions() {
  try {
    const [templatesRes, datasetsRes] = await Promise.all([
      ChartStatistices.GetTemplatesAsync({ limit: 1000 }),
      ChartStatistices.GetDatasetsAsync({ limit: 1000 }),
    ])
    chartTemplates.value = templatesRes.items || []
    datasets.value = datasetsRes.items || []
  }
  catch (e: any) {
    message.error(`加载选项失败: ${e.message || e}`)
  }
}

// 图表模板变化处理
function onChartTypeChange() {
  if (selectedTemplate.value?.fieldMappings) {
    // 初始化字段映射配置
    fieldConfigs.value = selectedTemplate.value.fieldMappings
      .filter(fm => fm.configType === ConfigType.FieldMapping)
      .map((fm) => {
        const config = new ChartConfig()
        config.fieldMapName = fm.name
        config.dataFieldName = null

        // 如果是复杂字段（对象数组），初始化子字段配置
        if (fm.dataType === 5 && fm.child && fm.child.length > 0) { // ObjectArray
          config.children = fm.child.map((child) => {
            const childConfig = new ChartConfig()
            childConfig.fieldMapName = child.name
            childConfig.dataFieldName = null
            childConfig.defaultValue = null
            return childConfig
          })
        }

        return config
      })

    // 初始化样式配置
    styleConfigs.value = selectedTemplate.value.fieldMappings
      .filter(fm => fm.configType === ConfigType.StyleConfig)
      .map((fm) => {
        const config = new ChartConfig()
        config.fieldMapName = fm.name
        config.dataFieldName = null
        return config
      })
  }
}

// 数据集变化处理
function onDatasetChange() {
  // 当数据集变化时，需要验证和重置字段映射配置
  if (!selectedDataset.value?.fieldsJson) {
    return
  }

  const availableFieldNames = selectedDataset.value.fieldsJson.map(field => field.name)
  let hasInvalidFields = false

  // 检查并重置不存在的字段映射
  fieldConfigs.value.forEach((config) => {
    if (isComplexField(config.fieldMapName)) {
      // 处理复杂字段（对象数组）
      if (Array.isArray(config.children)) {
        config.children.forEach((childConfig: ChartConfig) => {
          if (childConfig.dataFieldName && !availableFieldNames.includes(childConfig.dataFieldName)) {
            childConfig.dataFieldName = null // 重置不存在的字段
            hasInvalidFields = true
          }
        })
      }
    }
    else {
      // 处理简单字段
      if (config.dataFieldName && !availableFieldNames.includes(config.dataFieldName)) {
        config.dataFieldName = null // 重置不存在的字段
        hasInvalidFields = true
      }
    }
  })

  // 重置数据透视配置中不存在的字段
  resetInvalidPivotFields(availableFieldNames)

  // 显示提示信息
  if (fieldConfigs.value.length > 0) {
    if (hasInvalidFields) {
      message.warning('数据集已更换，部分字段映射已重置，请重新配置')
    }
    else {
      message.success('数据集已更换，字段映射配置仍然有效')
    }
  }
}

// 重置数据透视配置中无效的字段
function resetInvalidPivotFields(availableFieldNames: (string | null | undefined)[]) {
  let hasInvalidPivotFields = false

  // 重置行维度中不存在的字段
  if (pivotConfig.value.rowDimensions) {
    const validRowDimensions = pivotConfig.value.rowDimensions.filter(field =>
      field && availableFieldNames.includes(field),
    )
    if (validRowDimensions.length !== pivotConfig.value.rowDimensions.length) {
      pivotConfig.value.rowDimensions = validRowDimensions
      hasInvalidPivotFields = true
    }
  }

  // 重置列维度中不存在的字段
  if (pivotConfig.value.columnDimensions) {
    const validColumnDimensions = pivotConfig.value.columnDimensions.filter(field =>
      field && availableFieldNames.includes(field),
    )
    if (validColumnDimensions.length !== pivotConfig.value.columnDimensions.length) {
      pivotConfig.value.columnDimensions = validColumnDimensions
      hasInvalidPivotFields = true
    }
  }

  // 重置指标中不存在的字段
  if (pivotConfig.value.metrics) {
    const validMetrics = pivotConfig.value.metrics.filter(metric =>
      metric.valueField && availableFieldNames.includes(metric.valueField),
    )
    if (validMetrics.length !== pivotConfig.value.metrics.length) {
      pivotConfig.value.metrics = validMetrics
      hasInvalidPivotFields = true
    }
  }

  // 重置筛选条件中不存在的字段
  if (pivotConfig.value.filters) {
    const validFilters = pivotConfig.value.filters.filter(filter =>
      filter.field && availableFieldNames.includes(filter.field),
    )
    if (validFilters.length !== pivotConfig.value.filters.length) {
      pivotConfig.value.filters = validFilters
      hasInvalidPivotFields = true
    }
  }

  if (hasInvalidPivotFields) {
    message.warning('数据透视配置中的部分字段已重置')
  }
}

// 智能字段匹配建议
function suggestFieldMapping() {
  if (!selectedDataset.value?.fieldsJson || !selectedTemplate.value?.fieldMappings) {
    message.warning('请先选择图表模板和数据集')
    return
  }

  const availableFields = selectedDataset.value.fieldsJson
  let matchedCount = 0

  fieldConfigs.value.forEach((config) => {
    if (config.fieldMapName) {
      // 获取字段映射配置
      const fieldMapping = selectedTemplate.value!.fieldMappings!.find(fm => fm.name === config.fieldMapName)
      if (!fieldMapping)
        return

      if (isComplexField(config.fieldMapName)) {
        // 处理复杂字段的智能匹配
        if (Array.isArray(config.children)) {
          config.children.forEach((childConfig: ChartConfig) => {
            if (!childConfig.dataFieldName && childConfig.fieldMapName) {
              const childMapping = fieldMapping.child?.find(cm => cm.name === childConfig.fieldMapName)
              if (childMapping) {
                const suggestedField = findBestMatch(availableFields, childMapping.displayName || childMapping.name || '')
                if (suggestedField) {
                  childConfig.dataFieldName = suggestedField.name
                  matchedCount++
                }
              }
            }
          })
        }
      }
      else {
        // 处理简单字段的智能匹配
        if (!config.dataFieldName) {
          const suggestedField = findBestMatch(availableFields, fieldMapping.displayName || fieldMapping.name || '')
          if (suggestedField) {
            config.dataFieldName = suggestedField.name
            matchedCount++
          }
        }
      }
    }
  })

  if (matchedCount > 0) {
    message.success(`智能匹配完成，已匹配 ${matchedCount} 个字段`)
  }
  else {
    message.info('未找到合适的字段匹配')
  }
}

// 查找最佳匹配字段
function findBestMatch(availableFields: any[], targetName: string) {
  const target = targetName.toLowerCase()

  // 优先级1: 完全匹配
  let match = availableFields.find(field =>
    field.name?.toLowerCase() === target
    || field.description?.toLowerCase() === target,
  )
  if (match)
    return match

  // 优先级2: 包含匹配
  match = availableFields.find(field =>
    field.name?.toLowerCase().includes(target)
    || field.description?.toLowerCase().includes(target),
  )
  if (match)
    return match

  // 优先级3: 关键词匹配
  const keywords = ['时间', '日期', '年', '月', '日', 'time', 'date', 'year', 'month', 'day']
  if (keywords.some(keyword => target.includes(keyword))) {
    match = availableFields.find(field =>
      keywords.some(keyword =>
        field.name?.toLowerCase().includes(keyword)
        || field.description?.toLowerCase().includes(keyword),
      ),
    )
    if (match)
      return match
  }

  return null
}

// 数据透视配置相关方法
function addMetric() {
  if (!pivotConfig.value.metrics) {
    pivotConfig.value.metrics = []
  }
  pivotConfig.value.metrics.push(new Metric())
}

function removeMetric(index: number) {
  if (pivotConfig.value.metrics) {
    pivotConfig.value.metrics.splice(index, 1)
  }
}

function addFilter() {
  if (!pivotConfig.value.filters) {
    pivotConfig.value.filters = []
  }
  const filter = new FilterCondition()
  filter.operator = FilterOperator.Equal // 默认使用"等于"操作符
  pivotConfig.value.filters.push(filter)
}

function removeFilter(index: number) {
  if (pivotConfig.value.filters) {
    pivotConfig.value.filters.splice(index, 1)
  }
}

// 智能推荐数据透视配置
function suggestPivotConfig() {
  if (!selectedDataset.value?.fieldsJson) {
    message.warning('请先选择数据集')
    return
  }

  const fields = selectedDataset.value.fieldsJson
  let suggestedCount = 0

  // 智能推荐行维度：优先选择字符串类型的字段
  const stringFields = fields.filter(field => field.type === ChartFieldType.String)
  if (stringFields.length > 0 && (!pivotConfig.value.rowDimensions || pivotConfig.value.rowDimensions.length === 0)) {
    const firstStringField = stringFields[0]
    if (firstStringField && firstStringField.name) {
      pivotConfig.value.rowDimensions = [firstStringField.name]
      suggestedCount++
    }
  }

  // 智能推荐列维度：选择第二个字符串字段或日期字段
  const dateFields = fields.filter(field => field.type === ChartFieldType.Date)
  const candidateColumnFields = [...dateFields, ...stringFields.slice(1)]
  if (candidateColumnFields.length > 0 && (!pivotConfig.value.columnDimensions || pivotConfig.value.columnDimensions.length === 0)) {
    const firstCandidateField = candidateColumnFields[0]
    if (firstCandidateField && firstCandidateField.name) {
      pivotConfig.value.columnDimensions = [firstCandidateField.name]
      suggestedCount++
    }
  }

  // 智能推荐指标：选择数值类型字段
  if (numericFields.value.length > 0 && (!pivotConfig.value.metrics || pivotConfig.value.metrics.length === 0)) {
    const firstNumericField = numericFields.value[0]
    if (firstNumericField && firstNumericField.name) {
      const metric = new Metric()
      metric.valueField = firstNumericField.name
      metric.aggregation = AggregationType.Sum // 默认使用求和
      pivotConfig.value.metrics = [metric]
      suggestedCount++
    }
  }

  if (suggestedCount > 0) {
    message.success(`智能推荐完成，已配置 ${suggestedCount} 项设置`)
  }
  else {
    message.info('当前配置已完整，无需推荐')
  }
}

// 清空数据透视配置
function clearPivotConfig() {
  pivotConfig.value = new PivotConfig()
  message.success('数据透视配置已清空')
}

function openEditModal(record?: ChartChartManagement) {
  if (record) {
    editForm.value = { ...record }
    // 加载现有的字段映射和样式配置
    fieldConfigs.value = record.fieldConfigJson || []
    styleConfigs.value = record.styleConfigJson || []
    // 加载现有的数据透视配置
    pivotConfig.value = record.pivotConfig ? { ...record.pivotConfig } : new PivotConfig()

    // 如果已有模板选择，触发模板变化处理以确保配置结构正确
    if (record.chartTypeId) {
      nextTick(() => {
        onChartTypeChange()
        // 重新应用已保存的配置值
        if (record.fieldConfigJson) {
          mergeExistingFieldConfigs(record.fieldConfigJson)
        }
      })
    }
  }
  else {
    editForm.value = new ChartChartManagement()
    fieldConfigs.value = []
    styleConfigs.value = []
    pivotConfig.value = new PivotConfig()
  }
  editVisible.value = true
  activeTab.value = 'fieldMapping'
}

// 合并现有的字段配置到新的配置结构中
function mergeExistingFieldConfigs(existingConfigs: ChartConfig[]) {
  existingConfigs.forEach((existingConfig) => {
    const currentConfig = fieldConfigs.value.find(fc => fc.fieldMapName === existingConfig.fieldMapName)
    if (currentConfig) {
      currentConfig.dataFieldName = existingConfig.dataFieldName
      currentConfig.children = existingConfig.children
      currentConfig.defaultValue = existingConfig.defaultValue
    }
  })
}

async function handleEditOk() {
  try {
    if (!editForm.value.title) {
      message.warning('请填写图表标题')
      return
    }
    if (!editForm.value.chartTypeId) {
      message.warning('请选择图表模板')
      return
    }
    if (!editForm.value.chartDatasetId) {
      message.warning('请选择数据集')
      return
    }

    // 设置字段映射、样式配置和数据透视配置
    editForm.value.fieldConfigJson = fieldConfigs.value
    editForm.value.styleConfigJson = styleConfigs.value
    editForm.value.pivotConfig = pivotConfig.value

    if (Guid.isNotNull(editForm.value.id)) {
      await ChartStatistices.UpdateChart_PostAsync(
        { id: String(editForm.value.id) },
        editForm.value as ChartChartManagement,
      )
      message.success('更新成功')
    }
    else {
      await ChartStatistices.CreateChart_PostAsync(editForm.value as ChartChartManagement)
      message.success('创建成功')
    }
    editVisible.value = false
    tableRef.value?.search()
  }
  catch (e: any) {
    message.error(`操作失败: ${e.message || e}`)
  }
}

async function deleteChart(record: ChartChartManagement) {
  try {
    await ChartStatistices.DeleteChart_PostAsync({ id: String(record.id) })
    message.success('删除成功')
    tableRef.value?.search()
  }
  catch (e: any) {
    message.error(`删除失败: ${e.message || e}`)
  }
}

async function previewChart(record: ChartChartManagement) {
  try {
    const chartConfig = await ChartStatistices.PreviewChart_GetAsync({ id: String(record.id) })

    // 创建预览弹窗
    Modal.info({
      title: `图表预览 - ${record.title}`,
      content: h('div', {
        id: 'chart-preview-modal',
        style: { width: '100%', height: '400px' },
      }),
      width: 900,
      onOk() {
        // 弹窗关闭时清理图表实例
        const chartDom = document.getElementById('chart-preview-modal')
        if (chartDom && (chartDom as any).__echarts_instance__) {
          (chartDom as any).__echarts_instance__.dispose()
        }
      },
    })

    // 等待DOM渲染完成后初始化图表
    nextTick(() => {
      const chartDom = document.getElementById('chart-preview-modal')
      if (chartDom) {
        const chart = echarts.init(chartDom)
        chart.setOption(chartConfig)

        // 保存图表实例以便后续清理
        ;(chartDom as any).__echarts_instance__ = chart
      }
    })
  }
  catch (e: any) {
    message.error(`预览失败: ${e.message || e}`)
  }
}

// 刷新预览
async function refreshPreview() {
  // 验证必要的配置
  if (!selectedDataset.value?.id) {
    message.warning('请先选择数据集')
    return
  }

  if (!selectedTemplate.value?.id) {
    message.warning('请先选择图表模板')
    return
  }

  previewLoading.value = true
  try {
    // 构建预览数据
    const previewData = new ChartChartManagement()
    previewData.id = editForm.value.id
    previewData.title = editForm.value.title
    previewData.chartTypeId = editForm.value.chartTypeId
    previewData.chartDatasetId = editForm.value.chartDatasetId
    previewData.fieldConfigJson = fieldConfigs.value
    previewData.styleConfigJson = styleConfigs.value
    previewData.pivotConfig = pivotConfig.value
    previewData.updatedAt = editForm.value.updatedAt || dayjs()

    console.log(previewData)
    // 调用 PreviewChartByData_GetAsync 接口
    const chartConfig = await ChartStatistices.PreviewChartByData_PostAsync(
      {
        datasetId: String(selectedDataset.value.id),
        templateId: String(selectedTemplate.value.id),
      },
      previewData,
    )

    if (chartPreviewRef.value) {
      // 清理之前的图表实例
      if (chartInstance) {
        chartInstance.dispose()
      }

      chartInstance = echarts.init(chartPreviewRef.value)
      chartInstance.setOption(chartConfig)
    }
  }
  catch (e: any) {
    message.error(`预览失败: ${e.message || e}`)
  }
  finally {
    previewLoading.value = false
  }
}

onMounted(() => {
  loadOptions()
})
</script>
